/* Matrix Animations */

/* Glow Animation */
@keyframes glow {
    from {
        text-shadow: 0 0 20px rgb(0, 225, 0);
    }
    to {
        text-shadow: 0 0 30px rgb(0, 255, 0), 0 0 40px rgb(0, 255, 0);
    }
}

/* Wave Animation for Voice Visualization */
@keyframes wave {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* Typing Animation */
@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        border-color: transparent;
    }
    51%, 100% {
        border-color: rgb(0, 225, 0);
    }
}

.typing {
    overflow: hidden;
    border-right: 2px solid rgb(0, 225, 0);
    white-space: nowrap;
    animation: typing 3s steps(20, end), blink 1s step-end infinite;
}

/* Matrix Rain Animation */
@keyframes matrix-rain {
    0% {
        transform: translateY(-100vh);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh);
        opacity: 0;
    }
}

.matrix-rain::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(90deg, transparent 98%, rgba(0, 225, 0, 0.3) 100%),
        linear-gradient(180deg, transparent 98%, rgba(0, 225, 0, 0.3) 100%);
    background-size: 20px 20px;
    animation: matrix-rain 10s linear infinite;
    opacity: 0.1;
}

/* Fade In Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Pulse Animation */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Glitch Effect */
@keyframes glitch {
    0% {
        transform: translate(0);
    }
    20% {
        transform: translate(-2px, 2px);
    }
    40% {
        transform: translate(-2px, -2px);
    }
    60% {
        transform: translate(2px, 2px);
    }
    80% {
        transform: translate(2px, -2px);
    }
    100% {
        transform: translate(0);
    }
}

/* Hover Animations */
.btn:hover {
    animation: pulse 0.5s ease-in-out;
}

.hero-title:hover {
    animation: glitch 0.5s ease-in-out;
}

/* Scroll Animations */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in-up.visible {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px);
    transition: all 0.6s ease;
}

.fade-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
    transition: all 0.6s ease;
}

.fade-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

/* Loading Animation */
@keyframes loading {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.loading {
    animation: loading 1s linear infinite;
}

/* Matrix Code Effect */
@keyframes matrix-code {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        transform: translateY(10px);
    }
}

.matrix-code {
    animation: matrix-code 2s ease-in-out infinite;
}