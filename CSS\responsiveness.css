/* Responsive Design for Matrix Website */

/* Large Desktop */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
    }

    h1 {
        font-size: 4rem;
    }

    h2 {
        font-size: 3rem;
    }
}

/* Desktop */
@media (max-width: 1199px) {
    .hero-content {
        gap: 3rem;
    }

    .product-section {
        gap: 3rem;
    }
}

/* Tablet */
@media (max-width: 991px) {
    /* Navigation */
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(0, 0, 0, 0.98);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        border-top: 1px solid var(--matrix-green);
        padding: 2rem 0;
        gap: 1rem;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    /* Hero Section */
    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-text {
        order: 2;
    }

    .hero-image {
        order: 1;
    }

    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 2rem;
    }

    h3 {
        font-size: 1.5rem;
    }

    /* Product Sections */
    .product-section,
    .product-section.reverse {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .product-section.reverse .product-content {
        order: 2;
    }

    .product-section.reverse .product-visual {
        order: 1;
    }

    /* Terminal Window */
    .terminal-window {
        width: 100%;
        max-width: 400px;
    }

    /* Demo Section */
    .demo-buttons {
        flex-direction: column;
        align-items: center;
    }

    /* Join Section */
    .join-stats {
        gap: 2rem;
    }

    /* Footer */
    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
}

/* Mobile Large */
@media (max-width: 767px) {
    .container {
        padding: 0 15px;
    }

    /* Typography */
    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.8rem;
    }

    h3 {
        font-size: 1.3rem;
    }

    p {
        font-size: 1rem;
    }

    /* Navigation */
    .nav-container {
        padding: 0 15px;
    }

    .logo-text {
        font-size: 1.2rem;
    }

    /* Hero Section */
    .hero {
        min-height: 90vh;
    }

    .hero-content {
        min-height: 70vh;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    /* Sections */
    .section {
        padding: 3rem 0;
    }

    .section-title {
        margin-bottom: 2rem;
    }

    /* Matrix Text */
    .matrix-text {
        font-size: 1.1rem;
    }

    .matrix-text.final {
        font-size: 1.3rem;
    }

    /* Product Description */
    .product-description p {
        font-size: 1rem;
    }

    /* AI Visualization */
    .ai-visualization {
        height: 250px;
    }

    .ai-icon {
        font-size: 3rem;
    }

    .wave:nth-child(1) {
        width: 80px;
        height: 80px;
        margin: -40px 0 0 -40px;
    }

    .wave:nth-child(2) {
        width: 120px;
        height: 120px;
        margin: -60px 0 0 -60px;
    }

    .wave:nth-child(3) {
        width: 160px;
        height: 160px;
        margin: -80px 0 0 -80px;
    }

    .wave:nth-child(4) {
        width: 200px;
        height: 200px;
        margin: -100px 0 0 -100px;
    }

    /* Terminal */
    .terminal-window {
        width: 100%;
    }

    .terminal-body {
        padding: 15px;
        min-height: 150px;
    }

    /* Demo Section */
    .video-placeholder {
        height: 200px;
    }

    .play-button {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    /* Join Form */
    .form-group {
        flex-direction: column;
    }

    .email-input {
        min-width: 100%;
    }

    .join-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    /* Footer */
    .footer-links {
        flex-direction: column;
        gap: 1rem;
    }

    .footer-social {
        justify-content: center;
    }
}

/* Mobile Small */
@media (max-width: 479px) {
    /* Typography */
    h1 {
        font-size: 1.8rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    h3 {
        font-size: 1.2rem;
    }

    /* Navigation */
    .logo-img {
        height: 30px;
    }

    .logo-text {
        font-size: 1rem;
    }

    /* Buttons */
    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    /* Hero Section */
    .hero-content {
        gap: 1.5rem;
    }

    /* Sections */
    .section {
        padding: 2rem 0;
    }

    /* AI Visualization */
    .ai-visualization {
        height: 200px;
    }

    .ai-icon {
        font-size: 2.5rem;
    }

    /* Terminal */
    .terminal-header {
        padding: 8px 12px;
    }

    .terminal-title {
        font-size: 0.8rem;
    }

    .terminal-body {
        padding: 12px;
        min-height: 120px;
    }

    .prompt,
    .command {
        font-size: 0.9rem;
    }

    /* Demo Section */
    .video-placeholder {
        height: 150px;
    }

    .play-button {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    /* Social Links */
    .social-link {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
}

/* Landscape Mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .hero {
        min-height: 100vh;
    }

    .hero-content {
        min-height: 80vh;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .hero-text {
        order: 1;
    }

    .hero-image {
        order: 2;
    }

    .section {
        padding: 2rem 0;
    }
}