/* Matrix Website Styles */

/* CSS Variables */
:root {
    --matrix-green: rgb(0, 225, 0);
    --matrix-green-dark: rgb(0, 180, 0);
    --matrix-green-light: rgb(0, 255, 0);
    --matrix-black: #000000;
    --matrix-dark-gray: #0a0a0a;
    --matrix-gray: #1a1a1a;
    --matrix-light-gray: #333333;
    --matrix-white: #ffffff;
    --matrix-red: #ff0040;

    --font-primary: 'Orbitron', monospace;
    --font-secondary: '<PERSON><PERSON><PERSON>', sans-serif;

    --transition: all 0.3s ease;
    --border-radius: 4px;
    --box-shadow: 0 0 20px rgba(0, 225, 0, 0.3);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-secondary);
    background-color: var(--matrix-black);
    color: var(--matrix-white);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
}

h1 {
    font-size: 3.5rem;
    line-height: 1.2;
}

h2 {
    font-size: 2.5rem;
    line-height: 1.3;
}

h3 {
    font-size: 1.8rem;
    line-height: 1.4;
}

p {
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 30px;
    font-family: var(--font-primary);
    font-weight: 600;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: 2px solid;
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background-color: transparent;
    color: var(--matrix-green);
    border-color: var(--matrix-green);
}

.btn-primary:hover {
    background-color: var(--matrix-green);
    color: var(--matrix-black);
    box-shadow: var(--box-shadow);
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: transparent;
    color: var(--matrix-white);
    border-color: var(--matrix-white);
}

.btn-secondary:hover {
    background-color: var(--matrix-white);
    color: var(--matrix-black);
    transform: translateY(-2px);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    border-bottom: 1px solid var(--matrix-green);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-img {
    height: 40px;
    width: auto;
}

.logo-text {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: 900;
    color: var(--matrix-green);
    text-shadow: 0 0 10px var(--matrix-green);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: var(--matrix-white);
    text-decoration: none;
    font-family: var(--font-primary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 5px;
}

.nav-link:hover {
    color: var(--matrix-green);
    text-shadow: 0 0 5px var(--matrix-green);
}

/* Dropdown */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: rgba(0, 0, 0, 0.95);
    border: 1px solid var(--matrix-green);
    border-radius: var(--border-radius);
    list-style: none;
    min-width: 250px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: block;
    padding: 10px 20px;
    color: var(--matrix-white);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-link:hover {
    background-color: var(--matrix-green);
    color: var(--matrix-black);
}

/* Mobile Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--matrix-green);
    margin: 3px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, var(--matrix-black) 0%, var(--matrix-dark-gray) 100%);
    overflow: hidden;
}

.hero-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    min-height: 80vh;
}

.hero-text {
    z-index: 2;
}

.hero-title {
    color: var(--matrix-green);
    text-shadow: 0 0 20px var(--matrix-green);
    margin-bottom: 1rem;
    animation: glow 2s ease-in-out infinite alternate;
}

.hero-subtitle {
    color: var(--matrix-white);
    font-weight: 300;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image {
    position: relative;
    z-index: 2;
}

.hero-img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    border: 2px solid var(--matrix-green);
    box-shadow: var(--box-shadow);
}

.matrix-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(0, 225, 0, 0.1) 50%, transparent 70%);
    pointer-events: none;
}

/* Matrix Rain Effect */
.matrix-rain {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

/* Sections */
.section {
    padding: 5rem 0;
    position: relative;
}

.section:nth-child(even) {
    background-color: var(--matrix-dark-gray);
}

.section-title {
    text-align: center;
    color: var(--matrix-green);
    margin-bottom: 3rem;
    text-shadow: 0 0 10px var(--matrix-green);
}

/* What is Matrix Section */
.matrix-definition {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.matrix-text {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
    transition: var(--transition);
}

.matrix-text:hover {
    color: var(--matrix-green);
    text-shadow: 0 0 5px var(--matrix-green);
}

.matrix-text.highlight {
    color: var(--matrix-green);
    font-weight: 600;
    text-shadow: 0 0 10px var(--matrix-green);
}

.matrix-text.final {
    font-size: 1.5rem;
    color: var(--matrix-green);
    font-weight: 700;
    text-shadow: 0 0 15px var(--matrix-green);
}

/* Product Sections */
.product-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.product-section.reverse {
    grid-template-columns: 1fr 1fr;
}

.product-content {
    z-index: 2;
}

.product-description {
    margin-bottom: 2rem;
}

.product-description p {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.product-description .highlight {
    color: var(--matrix-green);
    font-weight: 600;
}

/* AI Visualization */
.ai-visualization {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
    background: radial-gradient(circle, rgba(0, 225, 0, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.ai-icon {
    font-size: 4rem;
    color: var(--matrix-green);
    z-index: 2;
    text-shadow: 0 0 20px var(--matrix-green);
}

.voice-waves {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.wave {
    position: absolute;
    border: 2px solid var(--matrix-green);
    border-radius: 50%;
    opacity: 0;
}

.wave:nth-child(1) {
    width: 100px;
    height: 100px;
    margin: -50px 0 0 -50px;
    animation: wave 2s linear infinite;
}

.wave:nth-child(2) {
    width: 150px;
    height: 150px;
    margin: -75px 0 0 -75px;
    animation: wave 2s linear infinite 0.5s;
}

.wave:nth-child(3) {
    width: 200px;
    height: 200px;
    margin: -100px 0 0 -100px;
    animation: wave 2s linear infinite 1s;
}

.wave:nth-child(4) {
    width: 250px;
    height: 250px;
    margin: -125px 0 0 -125px;
    animation: wave 2s linear infinite 1.5s;
}

/* OS Visualization */
.os-visualization {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
}

.terminal-window {
    width: 400px;
    background-color: var(--matrix-black);
    border: 2px solid var(--matrix-green);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.terminal-header {
    background-color: var(--matrix-gray);
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--matrix-green);
}

.terminal-buttons {
    display: flex;
    gap: 8px;
}

.terminal-buttons span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.btn-close {
    background-color: var(--matrix-red);
}

.btn-minimize {
    background-color: #ffaa00;
}

.btn-maximize {
    background-color: var(--matrix-green);
}

.terminal-title {
    color: var(--matrix-green);
    font-family: var(--font-primary);
    font-size: 0.9rem;
}

.terminal-body {
    padding: 20px;
    min-height: 200px;
    background-color: var(--matrix-black);
}

.terminal-line {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.prompt {
    color: var(--matrix-green);
    font-family: 'Courier New', monospace;
}

.command {
    color: var(--matrix-white);
    font-family: 'Courier New', monospace;
}

/* Demo Section */
.demo-section {
    text-align: center;
}

.demo-subtitle {
    color: var(--matrix-white);
    font-weight: 300;
    margin-bottom: 3rem;
    opacity: 0.9;
}

.demo-preview {
    max-width: 600px;
    margin: 0 auto 2rem;
    position: relative;
}

.video-placeholder {
    width: 100%;
    height: 300px;
    background: linear-gradient(135deg, var(--matrix-dark-gray) 0%, var(--matrix-gray) 100%);
    border: 2px solid var(--matrix-green);
    border-radius: var(--border-radius);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    cursor: pointer;
    transition: var(--transition);
}

.video-placeholder:hover {
    box-shadow: var(--box-shadow);
    transform: scale(1.02);
}

.play-button {
    width: 80px;
    height: 80px;
    background-color: var(--matrix-green);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 2rem;
    color: var(--matrix-black);
    transition: var(--transition);
}

.play-button:hover {
    transform: scale(1.1);
}

.demo-overlay {
    position: absolute;
    bottom: 20px;
    left: 20px;
    color: var(--matrix-green);
    font-family: var(--font-primary);
    font-weight: 600;
}

.demo-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Join Section */
.join-section {
    text-align: center;
    background: linear-gradient(135deg, var(--matrix-black) 0%, var(--matrix-dark-gray) 100%);
}

.join-description {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.9;
}

.join-form {
    max-width: 500px;
    margin: 0 auto 3rem;
}

.form-group {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.email-input {
    flex: 1;
    min-width: 250px;
    padding: 12px 20px;
    background-color: transparent;
    border: 2px solid var(--matrix-green);
    border-radius: var(--border-radius);
    color: var(--matrix-white);
    font-family: var(--font-secondary);
    font-size: 1rem;
    transition: var(--transition);
}

.email-input:focus {
    outline: none;
    box-shadow: var(--box-shadow);
    background-color: rgba(0, 225, 0, 0.05);
}

.email-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.join-stats {
    display: flex;
    justify-content: center;
    gap: 4rem;
    flex-wrap: wrap;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: var(--font-primary);
    font-size: 3rem;
    font-weight: 900;
    color: var(--matrix-green);
    text-shadow: 0 0 10px var(--matrix-green);
}

.stat-label {
    font-size: 1rem;
    color: var(--matrix-white);
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Footer */
.footer {
    background-color: var(--matrix-black);
    border-top: 1px solid var(--matrix-green);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 2rem;
}

.footer-links {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.footer-links a {
    color: var(--matrix-white);
    text-decoration: none;
    font-family: var(--font-primary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--matrix-green);
    text-shadow: 0 0 5px var(--matrix-green);
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background-color: transparent;
    border: 2px solid var(--matrix-green);
    border-radius: 50%;
    color: var(--matrix-green);
    text-decoration: none;
    transition: var(--transition);
}

.social-link:hover {
    background-color: var(--matrix-green);
    color: var(--matrix-black);
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--matrix-light-gray);
}

.footer-bottom p {
    color: var(--matrix-white);
    opacity: 0.7;
    margin: 0;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-green {
    color: var(--matrix-green);
}

.glow {
    text-shadow: 0 0 10px var(--matrix-green);
}

.highlight {
    color: var(--matrix-green);
    font-weight: 600;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--matrix-black);
}

::-webkit-scrollbar-thumb {
    background: var(--matrix-green);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--matrix-green-light);
}

/* Selection */
::selection {
    background-color: var(--matrix-green);
    color: var(--matrix-black);
}

::-moz-selection {
    background-color: var(--matrix-green);
    color: var(--matrix-black);
}

/* Page-specific Styles */

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: linear-gradient(135deg, var(--matrix-dark-gray) 0%, var(--matrix-gray) 100%);
    border: 1px solid var(--matrix-green);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow);
    border-color: var(--matrix-green-light);
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 225, 0, 0.1), transparent);
    transition: left 0.5s ease;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-icon {
    font-size: 3rem;
    color: var(--matrix-green);
    margin-bottom: 1rem;
    text-shadow: 0 0 10px var(--matrix-green);
}

.feature-card h3 {
    color: var(--matrix-green);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.feature-card p {
    color: var(--matrix-white);
    opacity: 0.9;
    line-height: 1.6;
}

/* Requirements Section */
.requirements-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
    margin-top: 3rem;
}

.req-column h3 {
    color: var(--matrix-green);
    text-align: center;
    margin-bottom: 2rem;
    font-size: 1.5rem;
}

.req-list {
    list-style: none;
    padding: 0;
}

.req-list li {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: rgba(0, 225, 0, 0.05);
    border: 1px solid rgba(0, 225, 0, 0.2);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.req-list li:hover {
    background: rgba(0, 225, 0, 0.1);
    border-color: var(--matrix-green);
}

.req-list li i {
    color: var(--matrix-green);
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

/* Download Options */
.download-options {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.download-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.download-info {
    text-align: center;
    margin-top: 2rem;
}

.download-info p {
    margin-bottom: 0.5rem;
    opacity: 0.8;
}

.download-info strong {
    color: var(--matrix-green);
}

/* Assistant Page Styles */

/* Capabilities Grid */
.capabilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.capability-card {
    background: linear-gradient(135deg, var(--matrix-dark-gray) 0%, var(--matrix-gray) 100%);
    border: 1px solid rgba(0, 225, 0, 0.3);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
}

.capability-card:hover {
    border-color: var(--matrix-green);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 225, 0, 0.2);
}

.capability-icon {
    font-size: 2.5rem;
    color: var(--matrix-green);
    margin-bottom: 1rem;
}

.capability-card h3 {
    color: var(--matrix-green);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.capability-card p {
    color: var(--matrix-white);
    opacity: 0.8;
    font-style: italic;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

/* Demo Interface */
.demo-interface {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-top: 3rem;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.voice-interface {
    text-align: center;
}

.microphone-container {
    margin-bottom: 2rem;
}

.mic-button {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--matrix-green) 0%, var(--matrix-green-dark) 100%);
    border: none;
    color: var(--matrix-black);
    font-size: 3rem;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 0 30px rgba(0, 225, 0, 0.5);
}

.mic-button:hover {
    transform: scale(1.1);
    box-shadow: 0 0 40px rgba(0, 225, 0, 0.7);
}

.mic-button.listening {
    background: linear-gradient(135deg, var(--matrix-red) 0%, #cc0033 100%);
    animation: pulse 1s ease-in-out infinite;
}

.mic-status {
    margin-top: 1rem;
    color: var(--matrix-white);
    font-family: var(--font-primary);
    opacity: 0.8;
}

.voice-visualization {
    margin-top: 2rem;
}

.sound-wave {
    display: flex;
    justify-content: center;
    align-items: end;
    gap: 4px;
    height: 60px;
}

.sound-wave .bar {
    width: 8px;
    background: var(--matrix-green);
    border-radius: 4px;
    animation: soundWave 1s ease-in-out infinite;
}

.sound-wave .bar:nth-child(1) { animation-delay: 0s; }
.sound-wave .bar:nth-child(2) { animation-delay: 0.1s; }
.sound-wave .bar:nth-child(3) { animation-delay: 0.2s; }
.sound-wave .bar:nth-child(4) { animation-delay: 0.3s; }
.sound-wave .bar:nth-child(5) { animation-delay: 0.4s; }

@keyframes soundWave {
    0%, 100% { height: 10px; }
    50% { height: 40px; }
}

/* Conversation Display */
.conversation-display {
    background: var(--matrix-dark-gray);
    border: 1px solid var(--matrix-green);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.conversation-header {
    background: var(--matrix-gray);
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--matrix-green);
}

.conversation-header h3 {
    color: var(--matrix-green);
    margin: 0;
    font-size: 1.1rem;
}

.clear-btn {
    background: transparent;
    border: 1px solid var(--matrix-green);
    color: var(--matrix-green);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-family: var(--font-primary);
    font-size: 0.8rem;
    transition: var(--transition);
}

.clear-btn:hover {
    background: var(--matrix-green);
    color: var(--matrix-black);
}

.conversation-body {
    height: 300px;
    overflow-y: auto;
    padding: 1rem;
}

.message {
    margin-bottom: 1rem;
    padding: 1rem;
    border-radius: var(--border-radius);
}

.user-message {
    background: rgba(0, 225, 0, 0.1);
    border-left: 3px solid var(--matrix-green);
    margin-left: 2rem;
}

.assistant-message {
    background: rgba(255, 255, 255, 0.05);
    border-left: 3px solid var(--matrix-white);
    margin-right: 2rem;
}

.message-content {
    color: var(--matrix-white);
    line-height: 1.5;
}

.message-content strong {
    color: var(--matrix-green);
}

/* Features Comparison */
.features-comparison {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-item {
    text-align: center;
    padding: 2rem;
}

.feature-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 1rem;
}

.feature-header i {
    font-size: 2.5rem;
    color: var(--matrix-green);
    margin-bottom: 1rem;
}

.feature-header h3 {
    color: var(--matrix-green);
    margin: 0;
    font-size: 1.2rem;
}

.feature-item p {
    color: var(--matrix-white);
    opacity: 0.9;
    line-height: 1.6;
}

/* About Page Styles */

/* Story Section */
.story-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.story-text p {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
    line-height: 1.8;
}

.story-text .highlight {
    color: var(--matrix-green);
    font-weight: 600;
    font-size: 1.3rem;
    text-shadow: 0 0 10px var(--matrix-green);
}

/* Mission Grid */
.mission-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.mission-item {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, var(--matrix-dark-gray) 0%, var(--matrix-gray) 100%);
    border: 1px solid rgba(0, 225, 0, 0.3);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.mission-item:hover {
    border-color: var(--matrix-green);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 225, 0, 0.2);
}

.mission-icon {
    font-size: 3rem;
    color: var(--matrix-green);
    margin-bottom: 1rem;
    text-shadow: 0 0 10px var(--matrix-green);
}

.mission-item h3 {
    color: var(--matrix-green);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.mission-item p {
    color: var(--matrix-white);
    opacity: 0.9;
    line-height: 1.6;
}

/* Values Section */
.values-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.value-item {
    padding: 2rem;
    border-left: 3px solid var(--matrix-green);
    background: rgba(0, 225, 0, 0.05);
    transition: var(--transition);
}

.value-item:hover {
    background: rgba(0, 225, 0, 0.1);
    transform: translateX(10px);
}

.value-item h3 {
    color: var(--matrix-green);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.value-item p {
    color: var(--matrix-white);
    opacity: 0.9;
    line-height: 1.6;
}

/* Team Section */
.team-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.team-intro {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.9;
    line-height: 1.8;
}

.team-stats {
    display: flex;
    justify-content: center;
    gap: 4rem;
    flex-wrap: wrap;
}

/* Contact Page Styles */

/* Contact Content */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 3rem;
}

/* Contact Form */
.contact-form-container {
    background: linear-gradient(135deg, var(--matrix-dark-gray) 0%, var(--matrix-gray) 100%);
    border: 1px solid var(--matrix-green);
    border-radius: var(--border-radius);
    padding: 2rem;
}

.contact-form {
    margin-top: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    color: var(--matrix-green);
    font-family: var(--font-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid rgba(0, 225, 0, 0.3);
    border-radius: var(--border-radius);
    color: var(--matrix-white);
    font-family: var(--font-secondary);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--matrix-green);
    box-shadow: 0 0 10px rgba(0, 225, 0, 0.3);
    background: rgba(0, 225, 0, 0.05);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-group select option {
    background: var(--matrix-black);
    color: var(--matrix-white);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Contact Methods */
.contact-methods {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-top: 2rem;
}

.contact-method {
    padding: 1.5rem;
    background: rgba(0, 225, 0, 0.05);
    border: 1px solid rgba(0, 225, 0, 0.2);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.contact-method:hover {
    border-color: var(--matrix-green);
    background: rgba(0, 225, 0, 0.1);
    transform: translateY(-2px);
}

.contact-icon {
    font-size: 2rem;
    color: var(--matrix-green);
    margin-bottom: 1rem;
}

.contact-method h3 {
    color: var(--matrix-green);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.contact-method p {
    color: var(--matrix-white);
    opacity: 0.8;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.contact-link {
    color: var(--matrix-green);
    text-decoration: none;
    font-family: var(--font-primary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.8rem;
    transition: var(--transition);
}

.contact-link:hover {
    color: var(--matrix-green-light);
    text-shadow: 0 0 5px var(--matrix-green);
}

/* FAQ Section */
.faq-content {
    max-width: 800px;
    margin: 0 auto;
    margin-top: 3rem;
}

.faq-item {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(0, 225, 0, 0.05);
    border-left: 3px solid var(--matrix-green);
    border-radius: var(--border-radius);
}

.faq-item h3 {
    color: var(--matrix-green);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.faq-item p {
    color: var(--matrix-white);
    opacity: 0.9;
    line-height: 1.6;
    margin: 0;
}

/* Pricing Page Styles */

/* Pricing Grid */
.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

/* Pricing Cards */
.pricing-card {
    background: linear-gradient(135deg, var(--matrix-dark-gray) 0%, var(--matrix-gray) 100%);
    border: 2px solid rgba(0, 225, 0, 0.3);
    border-radius: var(--border-radius);
    padding: 2rem;
    position: relative;
    transition: var(--transition);
    overflow: hidden;
}

.pricing-card:hover {
    border-color: var(--matrix-green);
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 225, 0, 0.2);
}

.pricing-card.featured {
    border-color: var(--matrix-green);
    box-shadow: 0 0 30px rgba(0, 225, 0, 0.3);
    transform: scale(1.05);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

/* Featured Badge */
.featured-badge {
    position: absolute;
    top: -1px;
    right: -1px;
    background: linear-gradient(135deg, var(--matrix-green) 0%, var(--matrix-green-light) 100%);
    color: var(--matrix-black);
    padding: 0.5rem 1rem;
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 0 var(--border-radius) 0 var(--border-radius);
}

/* Pricing Header */
.pricing-header {
    text-align: center;
    margin-bottom: 2rem;
}

.pricing-icon {
    font-size: 3rem;
    color: var(--matrix-green);
    margin-bottom: 1rem;
    text-shadow: 0 0 10px var(--matrix-green);
}

.pricing-card h3 {
    color: var(--matrix-green);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.pricing-price {
    margin-bottom: 1rem;
}

.price-amount {
    font-family: var(--font-primary);
    font-size: 3rem;
    font-weight: 900;
    color: var(--matrix-white);
    text-shadow: 0 0 10px var(--matrix-green);
}

.price-period {
    font-size: 1rem;
    color: var(--matrix-white);
    opacity: 0.7;
}

.savings {
    background: var(--matrix-green);
    color: var(--matrix-black);
    padding: 0.3rem 1rem;
    border-radius: 20px;
    font-family: var(--font-primary);
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: inline-block;
    margin-top: 0.5rem;
}

/* Pricing Features */
.pricing-features {
    margin-bottom: 2rem;
}

.pricing-features ul {
    list-style: none;
    padding: 0;
}

.pricing-features li {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem 0;
    border-bottom: 1px solid rgba(0, 225, 0, 0.1);
    color: var(--matrix-white);
    opacity: 0.9;
}

.pricing-features li:last-child {
    border-bottom: none;
}

.pricing-features li i {
    color: var(--matrix-green);
    font-size: 0.9rem;
    width: 16px;
    text-align: center;
}

/* Pricing Footer */
.pricing-footer {
    text-align: center;
}

.pricing-btn {
    width: 100%;
    margin-bottom: 1rem;
    padding: 1rem;
    font-size: 1rem;
}

.pricing-note {
    color: var(--matrix-white);
    opacity: 0.7;
    font-style: italic;
    font-size: 0.9rem;
    margin: 0;
}

/* Comparison Table */
.comparison-table {
    margin-top: 3rem;
    overflow-x: auto;
}

.features-table {
    width: 100%;
    border-collapse: collapse;
    background: linear-gradient(135deg, var(--matrix-dark-gray) 0%, var(--matrix-gray) 100%);
    border: 1px solid var(--matrix-green);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.features-table th,
.features-table td {
    padding: 1rem;
    text-align: center;
    border-bottom: 1px solid rgba(0, 225, 0, 0.2);
}

.features-table th {
    background: var(--matrix-green);
    color: var(--matrix-black);
    font-family: var(--font-primary);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.features-table td {
    color: var(--matrix-white);
}

.features-table td:first-child {
    text-align: left;
    font-weight: 600;
}

.features-table tr:hover {
    background: rgba(0, 225, 0, 0.05);
}

/* Table Icons */
.text-green {
    color: var(--matrix-green);
}

.text-red {
    color: var(--matrix-red);
}

.text-yellow {
    color: #ffaa00;
}

.text-gold {
    color: #ffd700;
}