// Matrix Website JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functionality
    initNavigation();
    initScrollAnimations();
    initMatrixRain();
    initTypingEffect();
    initFormHandling();
    initSmoothScrolling();
    initParallaxEffect();
});

// Navigation functionality
function initNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile menu toggle
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });

        // Close mobile menu when clicking on a link
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
    }

    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 100) {
            navbar.style.backgroundColor = 'rgba(0, 0, 0, 0.98)';
        } else {
            navbar.style.backgroundColor = 'rgba(0, 0, 0, 0.95)';
        }
    });
}

// Scroll animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Add animation classes to elements
    const animatedElements = document.querySelectorAll('.section-title, .matrix-text, .product-content, .product-visual');
    animatedElements.forEach((el, index) => {
        if (index % 2 === 0) {
            el.classList.add('fade-in-left');
        } else {
            el.classList.add('fade-in-right');
        }
        observer.observe(el);
    });

    // Special animation for hero elements
    const heroElements = document.querySelectorAll('.hero-title, .hero-subtitle, .hero-buttons');
    heroElements.forEach((el, index) => {
        el.classList.add('fade-in-up');
        setTimeout(() => {
            el.classList.add('visible');
        }, index * 200);
    });
}

// Matrix rain effect
function initMatrixRain() {
    const matrixContainer = document.querySelector('.matrix-rain');
    if (!matrixContainer) return;

    const characters = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';

    function createMatrixColumn() {
        const column = document.createElement('div');
        column.style.position = 'absolute';
        column.style.top = '-100px';
        column.style.left = Math.random() * window.innerWidth + 'px';
        column.style.color = 'rgba(0, 225, 0, 0.8)';
        column.style.fontSize = '14px';
        column.style.fontFamily = 'monospace';
        column.style.pointerEvents = 'none';
        column.style.zIndex = '1';

        let text = '';
        for (let i = 0; i < 20; i++) {
            text += characters.charAt(Math.floor(Math.random() * characters.length)) + '<br>';
        }
        column.innerHTML = text;

        matrixContainer.appendChild(column);

        // Animate the column
        let position = -100;
        const speed = Math.random() * 3 + 1;

        const animate = () => {
            position += speed;
            column.style.top = position + 'px';

            if (position > window.innerHeight) {
                matrixContainer.removeChild(column);
            } else {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    // Create matrix rain periodically
    setInterval(createMatrixColumn, 100);
}

// Typing effect for terminal
function initTypingEffect() {
    const typingElements = document.querySelectorAll('.typing');

    typingElements.forEach(element => {
        const text = element.textContent;
        element.textContent = '';
        element.style.borderRight = '2px solid rgb(0, 225, 0)';

        let i = 0;
        const typeWriter = () => {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            } else {
                // Blinking cursor effect
                setInterval(() => {
                    element.style.borderRight = element.style.borderRight === 'none' ?
                        '2px solid rgb(0, 225, 0)' : 'none';
                }, 500);
            }
        };

        // Start typing after a delay
        setTimeout(typeWriter, 1000);
    });
}

// Form handling
function initFormHandling() {
    const joinForm = document.querySelector('.join-form');

    if (joinForm) {
        joinForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const emailInput = joinForm.querySelector('.email-input');
            const email = emailInput.value.trim();

            if (validateEmail(email)) {
                // Simulate form submission
                showNotification('Welcome to the resistance! Check your email for further instructions.', 'success');
                emailInput.value = '';

                // Update stats (simulate)
                updateStats();
            } else {
                showNotification('Please enter a valid email address.', 'error');
            }
        });
    }
}

// Email validation
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

// Show notification
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${type === 'success' ? 'rgba(0, 225, 0, 0.9)' : 'rgba(255, 0, 64, 0.9)'};
        color: black;
        padding: 15px 20px;
        border-radius: 4px;
        font-family: 'Orbitron', monospace;
        font-weight: 600;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Update stats animation
function updateStats() {
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
        if (stat.textContent === '1,337') {
            const newNumber = parseInt(stat.textContent.replace(',', '')) + 1;
            stat.textContent = newNumber.toLocaleString();
            stat.style.animation = 'glow 0.5s ease-in-out';
        }
    });
}

// Smooth scrolling
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');

    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 80; // Account for fixed navbar

                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Parallax effect
function initParallaxEffect() {
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.hero-image, .matrix-rain');

        parallaxElements.forEach(element => {
            const speed = 0.5;
            element.style.transform = `translateY(${scrolled * speed}px)`;
        });
    });
}

// Voice wave animation
function animateVoiceWaves() {
    const waves = document.querySelectorAll('.wave');
    waves.forEach((wave, index) => {
        wave.style.animationDelay = `${index * 0.5}s`;
    });
}

// Initialize voice waves when AI section is visible
function initVoiceWaves() {
    const aiSection = document.querySelector('#ai-assistant');
    if (!aiSection) return;

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateVoiceWaves();
            }
        });
    }, { threshold: 0.5 });

    observer.observe(aiSection);
}

// Demo video placeholder interaction
function initDemoInteraction() {
    const videoPlaceholder = document.querySelector('.video-placeholder');
    const playButton = document.querySelector('.play-button');

    if (videoPlaceholder && playButton) {
        videoPlaceholder.addEventListener('click', function() {
            // Simulate video play
            showNotification('Demo video would play here. Coming soon!', 'success');

            // Add a visual effect
            playButton.style.transform = 'scale(1.2)';
            setTimeout(() => {
                playButton.style.transform = 'scale(1)';
            }, 200);
        });
    }
}

// Matrix code effect for background
function createMatrixCode() {
    const codeElements = document.querySelectorAll('.matrix-code');

    codeElements.forEach(element => {
        const characters = '01';
        let code = '';

        for (let i = 0; i < 50; i++) {
            code += characters.charAt(Math.floor(Math.random() * characters.length));
        }

        element.textContent = code;
    });
}

// Glitch effect on hover
function initGlitchEffects() {
    const glitchElements = document.querySelectorAll('.hero-title, .section-title');

    glitchElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            this.style.animation = 'glitch 0.3s ease-in-out';
        });

        element.addEventListener('animationend', function() {
            this.style.animation = '';
        });
    });
}

// Terminal command simulation
function initTerminalCommands() {
    const terminal = document.querySelector('.terminal-body');
    if (!terminal) return;

    const commands = [
        'matrix@system:~$ wake up neo...',
        'matrix@system:~$ initializing matrix os...',
        'matrix@system:~$ loading neural networks...',
        'matrix@system:~$ reality.exe has stopped working',
        'matrix@system:~$ welcome to the real world'
    ];

    let currentCommand = 0;

    setInterval(() => {
        if (currentCommand < commands.length - 1) {
            currentCommand++;
            const newLine = document.createElement('div');
            newLine.className = 'terminal-line';
            newLine.innerHTML = `<span class="prompt">matrix@system:~$</span> <span class="command">${commands[currentCommand].split('$ ')[1]}</span>`;
            terminal.appendChild(newLine);

            // Keep only last 5 commands
            if (terminal.children.length > 5) {
                terminal.removeChild(terminal.firstChild);
            }
        }
    }, 3000);
}

// Initialize additional effects
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        initVoiceWaves();
        initDemoInteraction();
        initGlitchEffects();
        initTerminalCommands();
        createMatrixCode();
    }, 1000);
});

// Resize handler
window.addEventListener('resize', function() {
    // Recalculate matrix rain on resize
    const matrixContainer = document.querySelector('.matrix-rain');
    if (matrixContainer) {
        // Clear existing rain
        matrixContainer.innerHTML = '';
    }
});

// Performance optimization: Throttle scroll events
function throttle(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply throttling to scroll events
window.addEventListener('scroll', throttle(function() {
    // Scroll-dependent animations go here
}, 16)); // ~60fps