<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Matrix - Join the Resistance</title>
    <link rel="stylesheet" href="../CSS/styles.css">
    <link rel="stylesheet" href="../CSS/animations.css">
    <link rel="stylesheet" href="../CSS/responsiveness.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-text">MATRIX</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item dropdown">
                    <a href="#" class="nav-link dropdown-toggle">Products <i class="fas fa-chevron-down"></i></a>
                    <ul class="dropdown-menu">
                        <li><a href="matrix-os.html" class="dropdown-link">Matrix OS</a></li>
                        <li><a href="matrix-assistant.html" class="dropdown-link">Matrix Voice AI Assistant</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a href="about.html" class="nav-link">About</a>
                </li>
                <li class="nav-item">
                    <a href="contact.html" class="nav-link">Contact</a>
                </li>
                <li class="nav-item">
                    <a href="pricing.html" class="nav-link">Pricing</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">Contact the Resistance</h1>
                    <h3 class="hero-subtitle">Ready to join the fight? Have questions? We're listening.</h3>
                </div>
            </div>
        </div>
        <div class="matrix-rain"></div>
    </section>

    <!-- Contact Section -->
    <section class="section">
        <div class="container">
            <div class="contact-content">
                <div class="contact-form-container">
                    <h2 class="section-title">Send a Message</h2>
                    <form class="contact-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="name">Name</label>
                                <input type="text" id="name" name="name" required>
                            </div>
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" name="email" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="subject">Subject</label>
                            <select id="subject" name="subject" required>
                                <option value="">Select a topic</option>
                                <option value="general">General Inquiry</option>
                                <option value="support">Technical Support</option>
                                <option value="partnership">Partnership</option>
                                <option value="bug">Bug Report</option>
                                <option value="feature">Feature Request</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="message">Message</label>
                            <textarea id="message" name="message" rows="6" required placeholder="Tell us how we can help you escape the system..."></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Send Message</button>
                    </form>
                </div>

                <div class="contact-info">
                    <h2 class="section-title">Other Ways to Connect</h2>
                    <div class="contact-methods">
                        <div class="contact-method">
                            <div class="contact-icon">
                                <i class="fab fa-discord"></i>
                            </div>
                            <h3>Discord</h3>
                            <p>Join our community of rebels and developers</p>
                            <a href="#" class="contact-link">Join Server</a>
                        </div>
                        <div class="contact-method">
                            <div class="contact-icon">
                                <i class="fab fa-github"></i>
                            </div>
                            <h3>GitHub</h3>
                            <p>View our public repositories, report issues, and track development updates</p>
                            <a href="#" class="contact-link">View Repositories</a>
                        </div>
                        <div class="contact-method">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <h3>Email</h3>
                            <p>For sensitive matters or direct communication</p>
                            <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
                        </div>
                        <div class="contact-method">
                            <div class="contact-icon">
                                <i class="fab fa-youtube"></i>
                            </div>
                            <h3>YouTube</h3>
                            <p>Watch tutorials, demos, and behind-the-scenes content</p>
                            <a href="#" class="contact-link">Subscribe</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">Frequently Asked Questions</h2>
            <div class="faq-content">
                <div class="faq-item">
                    <h3>Is Matrix really free?</h3>
                    <p>Matrix Linux OS and Matrix AI Assistant offer subscription-based pricing with free trials. While our software is proprietary, we believe in making powerful technology accessible to rebels everywhere.</p>
                </div>
                <div class="faq-item">
                    <h3>Why isn't Matrix open source?</h3>
                    <p>Matrix maintains proprietary control to ensure quality, security, and vision integrity. This allows us to move fast, break things, and rebuild them better without compromise.</p>
                </div>
                <div class="faq-item">
                    <h3>How do you ensure privacy?</h3>
                    <p>All processing happens locally on your machine. Your data never leaves your device unless you explicitly choose to share it.</p>
                </div>
                <div class="faq-item">
                    <h3>Can I contribute to the project?</h3>
                    <p>Matrix maintains proprietary control over its technology. However, we welcome feedback, bug reports, and feature suggestions from our community.</p>
                </div>
                <div class="faq-item">
                    <h3>When will Matrix Linux OS be available?</h3>
                    <p>We're working hard to bring you Matrix Linux OS. Join our newsletter to be the first to know when it's ready.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-links">
                    <a href="about.html">About</a>
                    <a href="https://github.com" target="_blank">GitHub</a>
                    <a href="contact.html">Contact</a>
                    <a href="#">License</a>
                </div>
                <div class="footer-social">
                    <a href="#" class="social-link"><i class="fab fa-discord"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Matrix. Created by rebels.</p>
            </div>
        </div>
    </footer>

    <script src="../JS/script.js"></script>
    <script>
        // Contact form handling
        document.addEventListener('DOMContentLoaded', function() {
            const contactForm = document.querySelector('.contact-form');

            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // Get form data
                    const formData = new FormData(contactForm);
                    const name = formData.get('name');
                    const email = formData.get('email');
                    const subject = formData.get('subject');
                    const message = formData.get('message');

                    // Simulate form submission
                    if (name && email && subject && message) {
                        showNotification('Message sent successfully! We\'ll get back to you soon.', 'success');
                        contactForm.reset();
                    } else {
                        showNotification('Please fill in all required fields.', 'error');
                    }
                });
            }
        });
    </script>
</body>
</html>