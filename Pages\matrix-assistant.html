<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Matrix AI Assistant - Voice-Powered Intelligence</title>
    <link rel="stylesheet" href="../CSS/styles.css">
    <link rel="stylesheet" href="../CSS/animations.css">
    <link rel="stylesheet" href="../CSS/responsiveness.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-text">MATRIX</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item dropdown">
                    <a href="#" class="nav-link dropdown-toggle">Products <i class="fas fa-chevron-down"></i></a>
                    <ul class="dropdown-menu">
                        <li><a href="matrix-os.html" class="dropdown-link">Matrix OS</a></li>
                        <li><a href="matrix-assistant.html" class="dropdown-link">Matrix Voice AI Assistant</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a href="about.html" class="nav-link">About</a>
                </li>
                <li class="nav-item">
                    <a href="contact.html" class="nav-link">Contact</a>
                </li>
                <li class="nav-item">
                    <a href="pricing.html" class="nav-link">Pricing</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">Matrix AI Assistant</h1>
                    <h3 class="hero-subtitle">A voice that listens. A mind that responds. Control your machine with a whisper.</h3>
                    <div class="hero-buttons">
                        <a href="#demo" class="btn btn-primary">Try Demo</a>
                        <a href="#capabilities" class="btn btn-secondary">See Capabilities</a>
                    </div>
                </div>
                <div class="hero-image">
                    <div class="ai-visualization">
                        <div class="voice-waves">
                            <div class="wave"></div>
                            <div class="wave"></div>
                            <div class="wave"></div>
                            <div class="wave"></div>
                        </div>
                        <i class="fas fa-microphone ai-icon"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="matrix-rain"></div>
    </section>

    <!-- Capabilities Section -->
    <section id="capabilities" class="section">
        <div class="container">
            <h2 class="section-title">Capabilities</h2>
            <div class="capabilities-grid">
                <div class="capability-card">
                    <div class="capability-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h3>Launch Applications</h3>
                    <p>"Open Visual Studio Code"</p>
                    <p>"Start Chrome and navigate to GitHub"</p>
                </div>
                <div class="capability-card">
                    <div class="capability-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3>Write Code</h3>
                    <p>"Create a Python function to sort arrays"</p>
                    <p>"Generate a React component for login"</p>
                </div>
                <div class="capability-card">
                    <div class="capability-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h3>Manage Emails</h3>
                    <p>"Read my latest emails"</p>
                    <p>"Send a meeting invite to the team"</p>
                </div>
                <div class="capability-card">
                    <div class="capability-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3>File Operations</h3>
                    <p>"Create a new project folder"</p>
                    <p>"Find all Python files in Documents"</p>
                </div>
                <div class="capability-card">
                    <div class="capability-icon">
                        <i class="fas fa-terminal"></i>
                    </div>
                    <h3>System Control</h3>
                    <p>"Run npm install in the current directory"</p>
                    <p>"Check system performance"</p>
                </div>
                <div class="capability-card">
                    <div class="capability-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3>AI Reasoning</h3>
                    <p>"Explain this error message"</p>
                    <p>"Suggest optimizations for this code"</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Section -->
    <section id="demo" class="section demo-section">
        <div class="container">
            <h2 class="section-title">Interactive Demo</h2>
            <div class="demo-interface">
                <div class="voice-interface">
                    <div class="microphone-container">
                        <button class="mic-button" id="micButton">
                            <i class="fas fa-microphone"></i>
                        </button>
                        <div class="mic-status">Click to speak</div>
                    </div>
                    <div class="voice-visualization">
                        <div class="sound-wave">
                            <div class="bar"></div>
                            <div class="bar"></div>
                            <div class="bar"></div>
                            <div class="bar"></div>
                            <div class="bar"></div>
                        </div>
                    </div>
                </div>
                <div class="conversation-display">
                    <div class="conversation-header">
                        <h3>Conversation</h3>
                        <button class="clear-btn">Clear</button>
                    </div>
                    <div class="conversation-body" id="conversationBody">
                        <div class="message assistant-message">
                            <div class="message-content">
                                <strong>Matrix:</strong> Hello! I'm your Matrix AI Assistant. Try saying "What can you do?" or "Open calculator".
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="section">
        <div class="container">
            <h2 class="section-title">Why Matrix AI?</h2>
            <div class="features-comparison">
                <div class="feature-item">
                    <div class="feature-header">
                        <i class="fas fa-shield-alt"></i>
                        <h3>100% Private</h3>
                    </div>
                    <p>All processing happens locally. Your voice never leaves your machine.</p>
                </div>
                <div class="feature-item">
                    <div class="feature-header">
                        <i class="fas fa-bolt"></i>
                        <h3>Lightning Fast</h3>
                    </div>
                    <p>Optimized local LLMs provide instant responses without internet dependency.</p>
                </div>
                <div class="feature-item">
                    <div class="feature-header">
                        <i class="fas fa-cogs"></i>
                        <h3>Fully Customizable</h3>
                    </div>
                    <p>Train it on your workflows, add custom commands, make it truly yours.</p>
                </div>
                <div class="feature-item">
                    <div class="feature-header">
                        <i class="fas fa-plug"></i>
                        <h3>Works Offline</h3>
                    </div>
                    <p>No internet required. Your AI assistant works anywhere, anytime.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section -->
    <section id="download" class="section join-section">
        <div class="container">
            <div class="join-content">
                <h2 class="section-title">Get Matrix AI Assistant</h2>
                <p class="join-description">
                    Ready to control your machine with your voice?<br>
                    Download and experience the future of human-computer interaction.
                </p>
                <div class="download-options">
                    <a href="#" class="btn btn-primary download-btn">
                        <i class="fas fa-download"></i>
                        Download for Windows
                    </a>
                    <a href="#" class="btn btn-primary download-btn">
                        <i class="fas fa-download"></i>
                        Download for macOS
                    </a>
                    <a href="#" class="btn btn-primary download-btn">
                        <i class="fas fa-download"></i>
                        Download for Linux
                    </a>
                </div>
                <div class="download-info">
                    <p><strong>Version:</strong> Matrix AI 1.0 "Morpheus"</p>
                    <p><strong>Size:</strong> ~500MB (includes local LLM)</p>
                    <p><strong>License:</strong> Proprietary - Free for personal use</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-links">
                    <a href="about.html">About</a>
                    <a href="https://github.com" target="_blank">GitHub</a>
                    <a href="contact.html">Contact</a>
                    <a href="#">License</a>
                </div>
                <div class="footer-social">
                    <a href="#" class="social-link"><i class="fab fa-discord"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Matrix. Created by rebels.</p>
            </div>
        </div>
    </footer>

    <script src="../JS/script.js"></script>
    <script>
        // Demo functionality
        document.addEventListener('DOMContentLoaded', function() {
            const micButton = document.getElementById('micButton');
            const conversationBody = document.getElementById('conversationBody');

            const demoResponses = {
                'what can you do': 'I can help you with various tasks like launching applications, writing code, managing files, and much more. Try asking me to "open calculator" or "create a new folder".',
                'open calculator': 'Opening Calculator application... [Calculator would launch here]',
                'create a new folder': 'Creating new folder "New Folder" on Desktop... [Folder created successfully]',
                'what time is it': 'The current time is ' + new Date().toLocaleTimeString(),
                'hello': 'Hello! How can I assist you today?',
                'help': 'I\'m here to help! You can ask me to open applications, manage files, write code, or answer questions. What would you like to do?'
            };

            micButton.addEventListener('click', function() {
                // Simulate voice recognition
                micButton.classList.add('listening');
                micButton.innerHTML = '<i class="fas fa-stop"></i>';

                setTimeout(() => {
                    const commands = Object.keys(demoResponses);
                    const randomCommand = commands[Math.floor(Math.random() * commands.length)];

                    addMessage('user', randomCommand);

                    setTimeout(() => {
                        addMessage('assistant', demoResponses[randomCommand]);
                        micButton.classList.remove('listening');
                        micButton.innerHTML = '<i class="fas fa-microphone"></i>';
                    }, 1000);
                }, 2000);
            });

            function addMessage(type, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}-message`;
                messageDiv.innerHTML = `
                    <div class="message-content">
                        <strong>${type === 'user' ? 'You' : 'Matrix'}:</strong> ${content}
                    </div>
                `;
                conversationBody.appendChild(messageDiv);
                conversationBody.scrollTop = conversationBody.scrollHeight;
            }
        });
    </script>
</body>
</html>